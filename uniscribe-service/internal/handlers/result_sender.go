package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/service/speech/stt/providers"
)

// TranscriptionResultPayload
type TranscriptionResultPayload struct {
	TaskID           int64                      `json:"taskId"`
	TaskType         constants.TaskType         `json:"taskType"`
	Text             string                     `json:"transcriptionText"`
	Duration         float64                    `json:"duration"`
	DetectedLanguage string                     `json:"detectedLanguage"`
	Segments         []providers.UnifiedSegment `json:"segments"`
	ServiceProvider  string                     `json:"serviceProvider"`
	ShowSpeakerName  bool                       `json:"showSpeakerName"`
	ShowTimestamps   bool                       `json:"showTimestamps"`
}

func (r TranscriptionResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeTranscription {
		return fmt.Errorf("task_type must be transcription")
	}
	if r.Text == "" {
		return fmt.Errorf("The transcript is empty")
	}
	return nil
}

type CorrectionResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Text     string             `json:"correctedText"`
}

func (r CorrectionResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeCorrection {
		return fmt.Errorf("task_type must be correction")
	}
	if r.Text == "" {
		return fmt.Errorf("text is required")
	}
	return nil
}

// SummaryResultPayload
type SummaryResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Summary  string             `json:"summary"`
}

func (r SummaryResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeSummary {
		return fmt.Errorf("task_type must be summary")
	}
	if r.Summary == "" {
		return fmt.Errorf("summary is required")
	}
	return nil
}

// KeywordResultPayload
type KeywordResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Keywords []string           `json:"keywords"`
}

func (r KeywordResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeKeyword {
		return fmt.Errorf("task_type must be keyword")
	}
	if len(r.Keywords) == 0 {
		return fmt.Errorf("keywords is required")
	}
	return nil
}

// OutlineResultPayload
type OutlineResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Outline  string             `json:"outline"`
}

func (r OutlineResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeOutline {
		return fmt.Errorf("task_type must be outline")
	}
	if r.Outline == "" {
		return fmt.Errorf("outline is required")
	}
	return nil
}

type QAResultPayload struct {
	TaskID       int64              `json:"taskId"`
	TaskType     constants.TaskType `json:"taskType"`
	QAExtraction []QAPair           `json:"qaExtraction"`
}

func (r QAResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeQAExtraction {
		return fmt.Errorf("task_type must be qa")
	}
	if len(r.QAExtraction) == 0 {
		return fmt.Errorf("qa_extraction is required")
	}
	return nil
}

// 任何任务类型失败，都可以用这个
type TaskFailedPyload struct {
	TaskID       int64              `json:"taskId"`
	TaskType     constants.TaskType `json:"taskType"`
	ErrorMessage string             `json:"errorMessage"`
}

func (r TaskFailedPyload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.ErrorMessage == "" {
		return fmt.Errorf("error_message is required")
	}
	return nil
}

type ResultPayload interface {
	Validate() error
}

type ResultSender interface {
	Send(result ResultPayload) error
}

type HTTPResultSender struct {
	client *http.Client
	host   string
}

func NewHTTPResultSender(host string) *HTTPResultSender {
	return &HTTPResultSender{
		host:   host,
		client: &http.Client{},
	}
}

func (s *HTTPResultSender) Send(result ResultPayload) error {
	apiUrl := s.host + "/tasks/result"
	if err := result.Validate(); err != nil {
		return fmt.Errorf("validation failed: %v", err)
	}

	requestBody, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %v", err)
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err)
		}
		return fmt.Errorf("unexpected status code: %d, %s", resp.StatusCode, body)
	}

	return nil
}

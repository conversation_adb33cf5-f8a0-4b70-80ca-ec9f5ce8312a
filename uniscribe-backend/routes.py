from resources.checkout import StripeCheckoutResource
from resources.customer_portal import CustomerPortalResource
from resources.entitlement import EntitlementResource
from resources.example import ExampleResource
from resources.export import ExportResource, ExportShareResource
from resources.plan import PlanConfigResource, SharerPlanConfigResource
from resources.task import (
    TaskNextResource,
    TaskResultResource,
    CreateTranscriptionTaskResource,
    CreateTextTasksResource,
)
from resources.transcription import (
    TranscriptionsByPageResource,
    TranscriptionsResource,
    TranscriptionResource,
    TranscriptionStatusResource,
    DeleteTranscriptionResource,
    TranscriptionSearchResource,
    AnonymousLatestTranscriptionResource,
    MigrateAnonymousTranscriptionResource,
    UnlockTranscriptionResource,
    TranscriptionSegmentResource,
)
from resources.upload import GenerateSignedUrlResource, CompleteUploadResource
from resources.usage import UsageResource
from resources.user import EmailCheckResource, UserResource
from resources.account import DeactivateAccountResource
from resources.webhook import StripeWebhookResource
from resources.appsumo import (
    AppSumoWebhookResource,
    AppSumoActivateResource,
)
from resources.share import ShareResource
from resources.youtube import (
    YoutubeInfoResource,
    CreateYoutubeTranscriptionTaskResource,
    YoutubeSubtitleListResource,
    YoutubeSubtitleDownloadResource,
)
from resources.redirect import RedirectResource


def register_routes(api):
    api.add_resource(ExampleResource, "/example")
    # 生成预签名 url
    api.add_resource(GenerateSignedUrlResource, "/upload/generate-signed-url")
    # 上传完成
    api.add_resource(CompleteUploadResource, "/upload/complete")
    # 获取所有转录文件
    api.add_resource(TranscriptionsResource, "/transcriptions")
    api.add_resource(TranscriptionsByPageResource, "/transcriptions/page")
    # 获取单个转录文件
    api.add_resource(
        TranscriptionResource,
        "/transcriptions/<int:transcription_file_id>",
        endpoint="transcription_file",
    )
    # 获取单个转录文件状态
    api.add_resource(
        TranscriptionStatusResource,
        "/transcriptions/<int:transcription_file_id>/status",
        endpoint="transcription_status",
    )
    # go service 获取下一个任务
    api.add_resource(
        TaskNextResource,
        "/tasks/next",
        endpoint="task_next",
    )
    # go service 提交任务结果
    api.add_resource(
        TaskResultResource, "/tasks/result", endpoint="task_result", methods=["POST"]
    )
    # 创建转录任务
    api.add_resource(CreateTranscriptionTaskResource, "/tasks/transcription")
    api.add_resource(
        CreateYoutubeTranscriptionTaskResource, "/tasks/youtube-transcription"
    )

    # go service 创建文本处理任务
    api.add_resource(CreateTextTasksResource, "/tasks/text")
    api.add_resource(ExportResource, "/export")

    # 支付
    api.add_resource(StripeCheckoutResource, "/checkout/stripe")
    api.add_resource(CustomerPortalResource, "/customer-portal")

    # webhook
    api.add_resource(StripeWebhookResource, "/webhook/stripe")
    api.add_resource(AppSumoWebhookResource, "/webhook/appsumo")

    # AppSumo OAuth
    api.add_resource(AppSumoActivateResource, "/auth/appsumo/activate")

    # 用户
    api.add_resource(UserResource, "/user")
    api.add_resource(EmailCheckResource, "/user/email-check")
    api.add_resource(DeactivateAccountResource, "/user/deactivate")

    # plan 配置
    api.add_resource(PlanConfigResource, "/plan-config")

    # usage
    api.add_resource(UsageResource, "/usage")

    # 分享页面
    api.add_resource(
        ShareResource,
        "/transcriptions/<int:transcription_file_id>/share",  # POST endpoint
        "/shares/<string:share_code>",  # GET endpoint
    )
    api.add_resource(ExportShareResource, "/shares/export")
    api.add_resource(SharerPlanConfigResource, "/sharer/plan-config")

    # youtube
    api.add_resource(YoutubeInfoResource, "/tools/youtube-info")
    api.add_resource(YoutubeSubtitleListResource, "/tools/youtube-subtitle-list")
    api.add_resource(
        YoutubeSubtitleDownloadResource, "/tools/youtube-subtitle-download"
    )

    # 删除转录文件
    api.add_resource(
        DeleteTranscriptionResource,
        "/transcriptions/<int:transcription_file_id>",
        endpoint="delete_transcription",
        methods=["DELETE"],
    )

    # 搜索转录文件
    api.add_resource(
        TranscriptionSearchResource,
        "/transcriptions/search",
        endpoint="search_transcription",
    )

    # 获取匿名用户最新文件
    api.add_resource(
        AnonymousLatestTranscriptionResource,
        "/transcriptions/anonymous/latest",
        endpoint="anonymous_latest_transcription",
    )
    # 迁移匿名用户的文件到新的登录用户
    api.add_resource(
        MigrateAnonymousTranscriptionResource,
        "/transcriptions/anonymous/migrate",
        endpoint="migrate_anonymous_transcription",
    )

    # 解锁转录文件（付费）
    api.add_resource(
        UnlockTranscriptionResource,
        "/transcriptions/<int:transcription_file_id>/unlock",
        endpoint="unlock_transcription",
    )

    # 权益相关接口
    api.add_resource(EntitlementResource, "/entitlements")

    # 更新单个segment的文本内容
    api.add_resource(
        TranscriptionSegmentResource,
        "/transcriptions/<int:transcription_file_id>/segments/<int:segment_id>",
        endpoint="transcription_segment",
    )

    # 重定向服务
    api.add_resource(
        RedirectResource,
        "/go/<string:target>",
        "/go/<string:target>/<string:campaign>",
        endpoint="redirect",
    )

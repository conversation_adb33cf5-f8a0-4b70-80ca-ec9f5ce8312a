import logging

from flask_restful import Resource, abort
from werkzeug.exceptions import HTTPException

from libs import reqparse
from controllers.export import export_text
from models.share import Share
from resources.auth import auth_required

logger = logging.getLogger(__name__)


def str_to_bool(value):
    """将字符串转换为布尔值"""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ("true", "1", "yes", "on")
    return bool(value)


class ExportResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    @auth_required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = str_to_bool(
            parser.get_argument(
                "showSpeakerName",
                type=str,
                required=False,
                location="json",
                default="true",
            )
        )
        show_timestamps = str_to_bool(
            parser.get_argument(
                "showTimestamps",
                type=str,
                required=False,
                location="json",
                default="true",
            )
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # 打印参数
        logger.info(
            f"file_type: {file_type}, file_id: {file_id}, show_speaker_name: {show_speaker_name}, show_timestamps: {show_timestamps}"
        )

        try:
            return export_text(file_type, file_id, show_speaker_name, show_timestamps)
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")


class ExportShareResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    # no auth required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # check file shared
        share = Share.get_by_file_id(file_id)
        if not share:
            abort(404, message="File not shared")

        try:
            return export_text(file_type, file_id, show_speaker_name, show_timestamps)
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")

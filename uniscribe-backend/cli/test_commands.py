"""
测试相关的CLI命令
"""

import click
from flask.cli import with_appcontext
import logging
from datetime import datetime

from models.transcription_file import TranscriptionFile
from models.task import Task
from controllers.task import create_transcription_task
from constants.transcription import TranscriptionFileStatus, TranscriptionFileSourceType
from constants.task import TranscriptionType
from libs.id_generator import id_generator
from models import db, insert_record

from . import admin_cli

logger = logging.getLogger(__name__)


@admin_cli.command("create-test-transcriptions")
@click.option("-n", "--count", default=5, help="创建的转录文件数量", type=int)
@click.option("-u", "--user-id", required=True, help="用户ID", type=int)
@with_appcontext
def create_test_transcriptions(count, user_id):
    """创建测试转录文件并开启转录任务

    参数:
        count: 创建的转录文件数量，默认5个
        user_id: 用户ID，必填

    示例:
        flask admin create-test-transcriptions -n 10 -u 7322591804778483712
    """
    logger.info("开始创建 %d 个测试转录文件，用户ID: %d", count, user_id)

    # 基础数据模板（基于你提供的数据）
    base_data = {
        "filename": "西班牙语",
        "file_type": "mp3",
        "file_url": "https://bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com/7322591804778483712-c72a67843076d631252aed6852987a9a.mp3",
        "file_key": "7322591804778483712-c72a67843076d631252aed6852987a9a.mp3",
        "file_size": 9646183,
        "fingerprint": "c72a67843076d631252aed6852987a9a",
        "duration": 768.072,
        "language_code": "es",
        "language": "spanish",
        "status": TranscriptionFileStatus.uploaded.id,  # 设为已上传状态
        "uploaded_time": None,
        "is_deleted": False,
        "transcription_type": TranscriptionType.TRANSCRIPT,
        "insufficient_minutes": 0,
        "source_type": TranscriptionFileSourceType.UPLOAD,
        "source_url": None,
        "media_download_time": 0,
    }

    created_files = []
    created_tasks = []

    try:
        for i in range(count):
            # 为每个文件生成唯一ID和时间戳
            file_id = id_generator.get_id()
            now = datetime.now()

            # 创建转录文件数据
            transcription_file = TranscriptionFile(
                id=file_id,
                user_id=user_id,
                filename=f"{base_data['filename']}_{i+1}",  # 添加序号区分
                file_type=base_data["file_type"],
                file_url=base_data["file_url"],
                file_key=base_data["file_key"],
                file_size=base_data["file_size"],
                fingerprint=base_data["fingerprint"],
                duration=base_data["duration"],
                language_code=base_data["language_code"],
                language=base_data["language"],
                status=base_data["status"],
                uploaded_time=base_data["uploaded_time"],
                is_deleted=base_data["is_deleted"],
                created_time=now,
                updated_time=now,
                transcription_type=base_data["transcription_type"],
                insufficient_minutes=base_data["insufficient_minutes"],
                source_type=base_data["source_type"],
                source_url=base_data["source_url"],
                media_download_time=base_data["media_download_time"],
            )

            # 插入转录文件记录
            insert_record(transcription_file)
            created_files.append(file_id)

            logger.info(
                "创建转录文件 %d: ID=%d, 文件名=%s",
                i + 1,
                file_id,
                transcription_file.filename,
            )

            # 创建转录任务
            try:
                task = create_transcription_task(user_id, file_id)
                created_tasks.append(task.id)
                logger.info(
                    "创建转录任务: 文件ID=%d, 任务ID=%d, 状态=%s",
                    file_id,
                    task.id,
                    task.status,
                )
            except Exception as e:
                logger.error("创建转录任务失败，文件ID=%d: %s", file_id, str(e))
                # 继续创建其他文件，不中断整个过程

        # 提交所有更改
        db.session.commit()

        logger.info(
            "成功创建 %d 个转录文件和 %d 个转录任务",
            len(created_files),
            len(created_tasks),
        )

        # 输出创建的文件和任务信息
        click.echo(f"✅ 成功创建 {len(created_files)} 个转录文件:")
        for i, file_id in enumerate(created_files):
            click.echo(f"   {i+1}. 文件ID: {file_id}")

        click.echo(f"✅ 成功创建 {len(created_tasks)} 个转录任务:")
        for i, task_id in enumerate(created_tasks):
            click.echo(f"   {i+1}. 任务ID: {task_id}")

        click.echo(f"\n📝 使用以下命令查看任务状态:")
        click.echo(f"   flask admin trigger-transcription <transcription_file_id>")

    except Exception as e:
        # 回滚事务
        db.session.rollback()
        logger.error("创建测试数据失败: %s", str(e))
        click.echo(f"❌ 创建失败: {str(e)}")
        raise
